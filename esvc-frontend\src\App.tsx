import { useState } from 'react';
import './styles/App.css';
import Header from './components/Header';
import Hero from './components/Hero';
import TreasuryDashboard from './components/TreasuryDashboard';
import ComparisonTable from './components/ComparisonTable';
import FAQ from './components/FAQ';
import Footer from './components/Footer';
import SignUp from './components/SignUp';
import ForgotPassword from './components/ForgotPassword';

type PageType = 'landing' | 'signup' | 'login' | 'forgot-password';

function App() {
  const [currentPage, setCurrentPage] = useState<PageType>('landing');

  const navigateToSignUp = () => setCurrentPage('signup');
  const navigateToLogin = () => setCurrentPage('login');
  const navigateToLanding = () => setCurrentPage('landing');
  const navigateToForgotPassword = () => setCurrentPage('forgot-password');

  const renderPage = () => {
    switch (currentPage) {
      case 'forgot-password':
        return (
          <div className="auth-page">
            <Header
              onNavigateToSignUp={navigateToSignUp}
              onNavigateToLogin={navigateToLogin}
              onNavigateToLanding={navigateToLanding}
            />
            <main className="auth-main">
              <ForgotPassword onBack={navigateToLogin} />
            </main>
            <Footer />
          </div>
        );
      case 'signup':
      case 'login':
        return (
          <div className="auth-page">
            <Header
              onNavigateToSignUp={navigateToSignUp}
              onNavigateToLogin={navigateToLogin}
              onNavigateToLanding={navigateToLanding}
            />
            <main className="auth-main">
              <SignUp initialMode={currentPage} />
            </main>
            <Footer />
          </div>
        );
      case 'landing':
      default:
        return (
          <div className="landing-page">
            <Header
              onNavigateToSignUp={navigateToSignUp}
              onNavigateToLogin={navigateToLogin}
              onNavigateToLanding={navigateToLanding}
            />
            <Hero />
            <TreasuryDashboard />
            <ComparisonTable />
            <FAQ />
            <Footer />
          </div>
        );
    }
  };

  return (
    <div className="app">
      {renderPage()}
    </div>
  );
}

export default App;
