/* Overview Container */
.overview-container {
  min-height: 100vh;
  background: #1A1A1A;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
}

/* Header */
.overview-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: #262626;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40px;
  z-index: 100;
  border-bottom: 1px solid #404040;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 48px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  font-size: 20px;
}

.logo-icon {
  font-size: 24px;
}

.header-nav {
  display: flex;
  gap: 32px;
}

.header-nav a {
  color: #CCCCCC;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  transition: color 0.3s ease;
}

.header-nav a:hover,
.header-nav a.active {
  color: #BF4129;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.profile-avatar {
  width: 40px;
  height: 40px;
  background: #BF4129;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
}

.profile-dropdown {
  color: #CCCCCC;
  font-size: 12px;
}

/* Main Content */
.overview-main {
  padding-top: 80px;
  min-height: calc(100vh - 80px);
}

/* Page Header */
.page-header {
  text-align: center;
  padding: 60px 40px 40px;
  max-width: 800px;
  margin: 0 auto;
}

.page-title {
  font-size: 48px;
  font-weight: 600;
  margin: 0 0 16px 0;
  background: linear-gradient(135deg, #BF4129 0%, #D19049 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  font-size: 16px;
  color: #CCCCCC;
  line-height: 24px;
  margin: 0;
}

/* Dashboard Layout */
.dashboard-layout {
  display: flex;
  gap: 40px;
  padding: 0 40px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Sidebar */
.dashboard-sidebar {
  width: 240px;
  background: #262626;
  border-radius: 16px;
  padding: 24px;
  height: fit-content;
  position: sticky;
  top: 120px;
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.sidebar-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: none;
  border: none;
  border-radius: 12px;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;
}

.sidebar-item:hover {
  background: #404040;
  color: #FFFFFF;
}

.sidebar-item.active {
  background: #BF4129;
  color: #FFFFFF;
}

.sidebar-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

/* Dashboard Content */
.dashboard-content {
  flex: 1;
  max-width: 920px;
}

.dashboard-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  align-items: flex-start;
  align-content: flex-start;
}

/* Dashboard Cards */
.dashboard-card {
  background: #262626;
  border-radius: 16px;
  padding: 24px;
  width: calc(50% - 12px);
  min-height: 140px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  transition: transform 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 12px;
  font-weight: 600;
  color: #999999;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0;
  line-height: 16px;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-value {
  font-size: 28px;
  font-weight: 700;
  color: #FFFFFF;
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.card-unit {
  font-size: 16px;
  font-weight: 500;
  color: #CCCCCC;
}

.card-change {
  font-size: 14px;
  font-weight: 500;
}

.card-change.positive {
  color: #4CAF50;
}

.card-change.negative {
  color: #FF6B6B;
}

/* Footer */
.overview-footer {
  background: #262626;
  margin-top: 80px;
  padding: 40px;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  font-size: 20px;
}

.footer-logo-icon {
  font-size: 24px;
}

.footer-nav {
  display: flex;
  gap: 32px;
}

.footer-nav a {
  color: #CCCCCC;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-nav a:hover {
  color: #BF4129;
}

.footer-social {
  display: flex;
  gap: 16px;
}

.social-link {
  width: 40px;
  height: 40px;
  background: #404040;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  font-size: 18px;
  transition: background 0.3s ease;
}

.social-link:hover {
  background: #BF4129;
}

.footer-bottom {
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid #404040;
}

.footer-bottom p {
  color: #999999;
  font-size: 14px;
  margin: 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .overview-header {
    padding: 0 20px;
    height: 70px;
  }

  .header-left {
    gap: 24px;
  }

  .header-nav {
    display: none;
  }

  .logo {
    font-size: 18px;
  }

  .page-header {
    padding: 40px 20px 30px;
  }

  .page-title {
    font-size: 32px;
  }

  .page-subtitle {
    font-size: 14px;
  }

  .dashboard-layout {
    flex-direction: column;
    padding: 0 20px;
    gap: 24px;
  }

  .dashboard-sidebar {
    width: 100%;
    position: static;
    order: 2;
  }

  .sidebar-nav {
    flex-direction: row;
    overflow-x: auto;
    gap: 12px;
    padding-bottom: 8px;
  }

  .sidebar-item {
    white-space: nowrap;
    min-width: fit-content;
  }

  .dashboard-content {
    order: 1;
  }

  .dashboard-cards {
    gap: 16px;
  }

  .dashboard-card {
    width: 100%;
    padding: 20px;
  }

  .card-value {
    font-size: 24px;
  }

  .footer-content {
    flex-direction: column;
    gap: 24px;
    text-align: center;
  }

  .footer-nav {
    flex-wrap: wrap;
    justify-content: center;
    gap: 16px;
  }

  .overview-footer {
    padding: 30px 20px;
  }
}
