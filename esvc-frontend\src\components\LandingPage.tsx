import React from 'react';
import Header from './Header';
import <PERSON> from './Hero';
import TreasuryDashboard from './TreasuryDashboard';
import ComparisonTable from './ComparisonTable';
import FAQ from './FAQ';
import Footer from './Footer';
import '../styles/components/LandingPage.css';

interface LandingPageProps {
  onNavigateToSignUp: () => void;
  onNavigateToLogin: () => void;
}

const LandingPage: React.FC<LandingPageProps> = ({ onNavigateToSignUp, onNavigateToLogin }) => {
  return (
    <div className="landing-page">
      <Header onNavigateToSignUp={onNavigateToSignUp} onNavigateToLogin={onNavigateToLogin} />
      <Hero />
      <TreasuryDashboard />
      <ComparisonTable />
      <FAQ />
      <Footer />
    </div>
  );
};

export default LandingPage;
