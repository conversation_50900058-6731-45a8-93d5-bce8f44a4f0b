.app {
  min-height: 100vh;
  background: #1A1A1A;
  color: #FFFFFF;
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  position: relative;
}

/* Landing Page */
.landing-page {
  min-height: 100vh;
  background: #1A1A1A;
}

/* Auth Page */
.auth-page {
  min-height: 100vh;
  background: #1A1A1A;
  display: flex;
  flex-direction: column;
  position: relative;
}

.auth-main {
  flex: 1;
  position: relative;
  z-index: 1;
  padding-top: 120px; /* Account for fixed header */
}

/* Ensure content doesn't overlap with fixed positioned header */
.app > * {
  position: relative;
  z-index: 1;
}

.app .header {
  z-index: 100;
}

/* Mobile adjustments */
@media (max-width: 768px) {
  .auth-main {
    padding-top: 100px;
  }
}
