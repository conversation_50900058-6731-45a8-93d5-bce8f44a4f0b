/* Forgot Password Container */
.forgot-password-container {
  min-height: 100vh;
  background: #1A1A1A;
  position: relative;
  overflow: hidden;
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', sans-serif;
}

/* Background Blur Gradients */
.forgot-password-container .blur-gradient {
  position: absolute;
  border-radius: 50%;
  z-index: 1;
}

.forgot-password-container .blur-gradient-1 {
  width: 316px;
  height: 316px;
  left: -150px;
  top: -150px;
  background: #CC6754;
  opacity: 0.2;
  filter: blur(200px);
}

.forgot-password-container .blur-gradient-2 {
  width: 239px;
  height: 239px;
  left: -50px;
  top: -50px;
  background: #D19049;
  opacity: 0.2;
  filter: blur(150px);
}

/* Main Content */
.forgot-password-content {
  position: relative;
  z-index: 10;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120px 24px 40px;
}

/* Back Button */
.back-button {
  position: absolute;
  top: 140px;
  left: 24px;
  background: none;
  border: none;
  color: #BF4129;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: color 0.3s ease;
}

.back-button:hover {
  color: #D19049;
}

.back-button span {
  font-size: 18px;
}

/* Form Container */
.forgot-password-form {
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 48px;
  width: 100%;
  max-width: 480px;
  text-align: center;
}

.forgot-password-form h1 {
  color: #FFFFFF;
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.forgot-password-form p {
  color: #CCCCCC;
  font-size: 16px;
  margin: 0 0 32px 0;
  line-height: 1.5;
}

/* Form Groups */
.form-group {
  margin-bottom: 24px;
  text-align: left;
}

.form-group label {
  display: block;
  color: #FFFFFF;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  height: 56px;
  background: #404040;
  border: 1px solid #525252;
  border-radius: 12px;
  padding: 0 16px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #BF4129;
  background: #4A4A4A;
}

.form-input::placeholder {
  color: #999999;
}

.toggle-password {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #999999;
  cursor: pointer;
  font-size: 18px;
}

/* Verification Code */
.verification-code {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin: 32px 0;
}

.code-input {
  width: 56px;
  height: 56px;
  background: #404040;
  border: 1px solid #525252;
  border-radius: 12px;
  text-align: center;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.code-input:focus {
  outline: none;
  border-color: #BF4129;
  background: #4A4A4A;
}

.code-input.invalid {
  border-color: #FF4444;
  background: rgba(255, 68, 68, 0.1);
}

/* Error Message */
.error-message {
  color: #FF4444;
  font-size: 14px;
  margin: 16px 0 0 0;
  text-align: center;
}

/* Primary Button */
.btn-primary {
  width: 100%;
  height: 56px;
  background: #BF4129;
  border: none;
  border-radius: 12px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 24px;
}

.btn-primary:hover:not(:disabled) {
  background: #D19049;
  transform: translateY(-2px);
}

.btn-primary:disabled {
  background: #666666;
  cursor: not-allowed;
  transform: none;
}

/* Resend Section */
.resend-section {
  margin: 24px 0;
  text-align: center;
}

.resend-section p {
  color: #CCCCCC;
  font-size: 14px;
  margin: 0 0 8px 0;
}

.resend-link {
  background: none;
  border: none;
  color: #BF4129;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
}

.resend-link:hover {
  color: #D19049;
}

/* Change Email Button */
.change-email-btn {
  background: none;
  border: none;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 auto;
  transition: color 0.3s ease;
}

.change-email-btn:hover {
  color: #FFFFFF;
}

/* Password Requirements */
.password-requirements {
  margin: 16px 0 24px 0;
  text-align: left;
}

.requirement {
  color: #999999;
  font-size: 14px;
  margin: 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.requirement::before {
  content: '○';
  color: #666666;
  font-size: 12px;
}

.requirement.valid {
  color: #4CAF50;
}

.requirement.valid::before {
  content: '●';
  color: #4CAF50;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .forgot-password-content {
    padding: 100px 16px 40px;
  }
  
  .back-button {
    top: 120px;
    left: 16px;
  }
  
  .forgot-password-form {
    padding: 32px 24px;
  }
  
  .forgot-password-form h1 {
    font-size: 28px;
  }
  
  .verification-code {
    gap: 8px;
  }
  
  .code-input {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }
}
