import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/components/Overview.css';

interface OverviewProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const Overview: React.FC<OverviewProps> = ({ 
  onNavigateToSignUp, 
  onNavigateToLogin, 
  onNavigateToLanding 
}) => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('overview');

  const sidebarItems = [
    { id: 'overview', label: 'Overview', icon: '📊', active: true },
    { id: 'live-reports', label: 'Live Reports', icon: '📈' },
    { id: 'daily-transactions', label: 'Daily Transactions', icon: '💳' },
    { id: 'real-time-trading', label: 'Real Time Trading', icon: '⚡' },
    { id: 'terminal-esvc', label: 'Terminal ESVC', icon: '💻' },
    { id: 'iso-distribution', label: 'ISO Distribution', icon: '🔄' },
    { id: 'view-analytics', label: 'View Analytics', icon: '📊' }
  ];

  const dashboardCards = [
    {
      title: 'TOTAL AMOUNT OF ESVC STAKED',
      value: '177,874,389.00',
      unit: 'ESVC',
      change: '+2.4% Today',
      changeType: 'positive'
    },
    {
      title: 'FUNDS AVAILABLE FOR DAILY ISO PAYOUTS',
      value: '$609,185',
      change: '+7.8% Today',
      changeType: 'positive'
    },
    {
      title: 'FUNDS AVAILABLE TO FUND STARTUPS',
      value: '$43,700',
      change: '+4.8% Today',
      changeType: 'positive'
    },
    {
      title: 'TOTAL NUMBER OF STAKERS',
      value: '11,302',
      change: '+3.8% Today',
      changeType: 'positive'
    },
    {
      title: 'TOTAL ESVC SOLD TO DATE',
      value: '$29,400,200.00',
      change: '+2.4% Today',
      changeType: 'positive'
    },
    {
      title: 'TOTAL PROFIT GENERATED',
      value: '$43,700',
      change: '+1.8% Today',
      changeType: 'positive'
    }
  ];

  const handleSidebarClick = (itemId: string) => {
    setActiveTab(itemId);
  };

  return (
    <div className="overview-container">
      {/* Header */}
      <header className="overview-header">
        <div className="header-left">
          <div className="logo">
            <span className="logo-icon">🏛️</span>
            <span className="logo-text">ESVC</span>
          </div>
          <nav className="header-nav">
            <a href="#" onClick={() => navigate('/')}>Home</a>
            <a href="#" className="active">Stake ESVC</a>
            <a href="#">Get Funding</a>
            <a href="#">Trade Challenge</a>
            <a href="#">Contact Us</a>
          </nav>
        </div>
        <div className="header-right">
          <div className="user-profile">
            <div className="profile-avatar">A</div>
            <span className="profile-dropdown">▼</span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="overview-main">
        {/* Page Title */}
        <div className="page-header">
          <h1 className="page-title">Treasury Dashboard</h1>
          <p className="page-subtitle">
            Live insights into how funds are being held (ROI %), performance and statuses are 
            summarized. Empowering you to make smart trades.
          </p>
        </div>

        <div className="dashboard-layout">
          {/* Sidebar */}
          <aside className="dashboard-sidebar">
            <nav className="sidebar-nav">
              {sidebarItems.map((item) => (
                <button
                  key={item.id}
                  className={`sidebar-item ${activeTab === item.id ? 'active' : ''}`}
                  onClick={() => handleSidebarClick(item.id)}
                >
                  <span className="sidebar-icon">{item.icon}</span>
                  <span className="sidebar-label">{item.label}</span>
                </button>
              ))}
            </nav>
          </aside>

          {/* Main Dashboard Content */}
          <div className="dashboard-content">
            <div className="dashboard-cards">
              {dashboardCards.map((card, index) => (
                <div key={index} className="dashboard-card">
                  <div className="card-header">
                    <h3 className="card-title">{card.title}</h3>
                  </div>
                  <div className="card-content">
                    <div className="card-value">
                      {card.value}
                      {card.unit && <span className="card-unit">{card.unit}</span>}
                    </div>
                    <div className={`card-change ${card.changeType}`}>
                      {card.change}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="overview-footer">
        <div className="footer-content">
          <div className="footer-logo">
            <span className="footer-logo-icon">🏛️</span>
            <span className="footer-logo-text">ESVC</span>
          </div>
          <nav className="footer-nav">
            <a href="#">Home</a>
            <a href="#">Stake ESVC</a>
            <a href="#">Get Funding</a>
            <a href="#">Trade Challenge</a>
            <a href="#">Contact Us</a>
          </nav>
          <div className="footer-social">
            <a href="#" className="social-link">📘</a>
            <a href="#" className="social-link">🐦</a>
            <a href="#" className="social-link">📷</a>
            <a href="#" className="social-link">💼</a>
          </div>
        </div>
        <div className="footer-bottom">
          <p>© 2024 ESVC Capital. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default Overview;
