import React, { useState } from 'react';
import '../styles/components/SignUp.css';
import appleIcon from '../assets/apple.png';
import ForgotPassword from './ForgotPassword';

interface SignUpProps {
  initialMode?: 'signup' | 'login';
}

const SignUp: React.FC<SignUpProps> = ({ initialMode = 'signup' }) => {
  const [isSignUp, setIsSignUp] = useState(initialMode === 'signup');
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    console.log('Form submitted:', formData);
  };

  const handleForgotPassword = () => {
    setShowForgotPassword(true);
  };

  const handleBackFromForgotPassword = () => {
    setShowForgotPassword(false);
  };

  // Show forgot password component if requested
  if (showForgotPassword) {
    return <ForgotPassword onBack={handleBackFromForgotPassword} />;
  }

  return (
    <div className="signup-page">
      {/* Background blur gradients */}
      <div className="blur-gradient blur-gradient-1"></div>
      <div className="blur-gradient blur-gradient-2"></div>

      <div className="signup-container">
        <div className="signup-content">
          {/* Header */}
          <div className="signup-header">
            <h1 className="signup-title">Welcome to ESVC Capital</h1>
            
            <div className="auth-tabs">
              <button
                className={`auth-tab ${isSignUp ? 'active' : ''}`}
                onClick={() => setIsSignUp(true)}
              >
                Sign Up
              </button>
              <button
                className={`auth-tab ${!isSignUp ? 'active' : ''}`}
                onClick={() => setIsSignUp(false)}
              >
                Login
              </button>
            </div>
          </div>

          {/* Form */}
          <div className="signup-form-container">
            <h2 className="form-title">
              {isSignUp ? 'Create Your Account to Start Earning' : 'Access Your Staking Dashboard'}
            </h2>

            {/* Social Login Buttons */}
            <div className="social-login">
              <button className="social-btn apple-btn">
                <img src={appleIcon} alt="Apple" className="social-icon" />
                Continue with Apple
              </button>
              <button className="social-btn google-btn">
                <svg className="social-icon" width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path d="M19.8 10.2c0-.6-.1-1.2-.2-1.8H10v3.4h5.5c-.2 1.2-.9 2.3-1.9 3v2.5h3.1c1.8-1.7 2.8-4.1 2.8-7.1z" fill="#4285F4"/>
                  <path d="M10 20c2.6 0 4.8-.9 6.4-2.4l-3.1-2.4c-.9.6-2 .9-3.3.9-2.5 0-4.7-1.7-5.5-4H1.3v2.5C2.9 17.3 6.2 20 10 20z" fill="#34A853"/>
                  <path d="M4.5 11.9c-.2-.6-.3-1.2-.3-1.9s.1-1.3.3-1.9V5.6H1.3C.5 7.1 0 8.5 0 10s.5 2.9 1.3 4.4l3.2-2.5z" fill="#FBBC05"/>
                  <path d="M10 4c1.4 0 2.7.5 3.7 1.4l2.8-2.8C14.8.9 12.6 0 10 0 6.2 0 2.9 2.7 1.3 6.6l3.2 2.5C5.3 5.7 7.5 4 10 4z" fill="#EA4335"/>
                </svg>
                Continue with Google
              </button>
              <button className="social-btn microsoft-btn">
                <svg className="social-icon" width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path d="M0 0h9.5v9.5H0V0z" fill="#F25022"/>
                  <path d="M10.5 0H20v9.5h-9.5V0z" fill="#7FBA00"/>
                  <path d="M0 10.5h9.5V20H0v-9.5z" fill="#00A4EF"/>
                  <path d="M10.5 10.5H20V20h-9.5v-9.5z" fill="#FFB900"/>
                </svg>
                Continue with Microsoft
              </button>
            </div>

            <div className="divider">
              <span>Or</span>
            </div>

            {/* Email/Password Form */}
            <form onSubmit={handleSubmit} className="signup-form">
              <div className="form-group">
                <label htmlFor="email" className="form-label">Email Address</label>
                <div className="input-wrapper">
                  <svg className="input-icon" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M2.5 4.5L8 8.5L13.5 4.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <rect x="1" y="3" width="14" height="10" rx="2" stroke="currentColor" strokeWidth="1.5"/>
                  </svg>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                    className="form-input"
                    required
                  />
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="password" className="form-label">
                  {isSignUp ? 'Create Password' : 'Password'}
                </label>
                <div className="input-wrapper">
                  <svg className="input-icon" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <rect x="3" y="7" width="10" height="7" rx="2" stroke="currentColor" strokeWidth="1.5"/>
                    <path d="M5 7V5a3 3 0 0 1 6 0v2" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                  </svg>
                  <input
                    type={showPassword ? 'text' : 'password'}
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    placeholder={isSignUp ? "Create a strong password" : "Enter your password"}
                    className="form-input"
                    required
                  />
                  <button
                    type="button"
                    className="password-toggle"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M1 8s3-5 7-5 7 5 7 5-3 5-7 5-7-5-7-5z" stroke="currentColor" strokeWidth="1.5"/>
                        <circle cx="8" cy="8" r="2" stroke="currentColor" strokeWidth="1.5"/>
                      </svg>
                    ) : (
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M1 8s3-5 7-5 7 5 7 5-3 5-7 5-7-5-7-5z" stroke="currentColor" strokeWidth="1.5"/>
                        <circle cx="8" cy="8" r="2" stroke="currentColor" strokeWidth="1.5"/>
                        <path d="M1 1l14 14" stroke="currentColor" strokeWidth="1.5"/>
                      </svg>
                    )}
                  </button>
                </div>

                {isSignUp && (
                  <div className="password-requirements">
                    <p>Your password must:</p>
                    <ul>
                      <li>Be at least 8 characters long</li>
                      <li>Include at least one uppercase letter (A-Z)</li>
                      <li>Include at least one lowercase letter (a-z)</li>
                      <li>Include at least one number (0-9)</li>
                      <li>Include at least one special character (!@#$%^&*)</li>
                    </ul>
                  </div>
                )}

                {!isSignUp && (
                  <div className="forgot-password">
                    <a href="#" className="forgot-password-link">Forgot Password?</a>
                  </div>
                )}
              </div>

              {isSignUp && (
                <div className="form-group">
                  <label htmlFor="confirmPassword" className="form-label">Re-Enter New Password</label>
                  <div className="input-wrapper">
                    <svg className="input-icon" width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <rect x="3" y="7" width="10" height="7" rx="2" stroke="currentColor" strokeWidth="1.5"/>
                      <path d="M5 7V5a3 3 0 0 1 6 0v2" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                    </svg>
                    <input
                      type={showConfirmPassword ? 'text' : 'password'}
                      id="confirmPassword"
                      name="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      placeholder="Re-enter your new password"
                      className="form-input"
                      required
                    />
                    <button
                      type="button"
                      className="password-toggle"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    >
                      {showConfirmPassword ? (
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                          <path d="M1 8s3-5 7-5 7 5 7 5-3 5-7 5-7-5-7-5z" stroke="currentColor" strokeWidth="1.5"/>
                          <circle cx="8" cy="8" r="2" stroke="currentColor" strokeWidth="1.5"/>
                        </svg>
                      ) : (
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                          <path d="M1 8s3-5 7-5 7 5 7 5-3 5-7 5-7-5-7-5z" stroke="currentColor" strokeWidth="1.5"/>
                          <circle cx="8" cy="8" r="2" stroke="currentColor" strokeWidth="1.5"/>
                          <path d="M1 1l14 14" stroke="currentColor" strokeWidth="1.5"/>
                        </svg>
                      )}
                    </button>
                  </div>
                </div>
              )}

              {!isSignUp && (
                <div className="forgot-password-link-container">
                  <button
                    type="button"
                    className="forgot-password-link"
                    onClick={handleForgotPassword}
                  >
                    Forgot Password?
                  </button>
                </div>
              )}

              <button type="submit" className="signup-btn">
                {isSignUp ? 'Sign Up' : 'Login'}
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignUp;
